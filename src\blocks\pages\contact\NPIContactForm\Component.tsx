'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { NPIFAQ } from '@/components/ui/npi-faq'
import { NPIContactInfo } from '@/components/ui/npi-contact-info'
import { NPIGoogleMap } from '@/components/ui/npi-google-map'
import { Mail, Send, User, MessageSquare } from 'lucide-react'

interface NPIContactFormProps {
  title?: string
  description?: string
}

export const NPIContactFormBlock: React.FC<NPIContactFormProps> = ({
  title = 'Contact Us',
  description = "Get in touch with the NPI team. We're here to answer your questions, provide information, and explore collaboration opportunities.",
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    subject: '',
    category: '',
    message: '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Reset form
    setFormData({
      name: '',
      email: '',
      organization: '',
      subject: '',
      category: '',
      message: '',
    })
    setIsSubmitting(false)
    alert('Thank you for your message! We will get back to you soon.')
  }

  const categories = [
    'General Inquiry',
    'Partnership Opportunity',
    'Research Collaboration',
    'Media & Press',
    'IKIA Database Access',
    'Program Information',
    'Technical Support',
    'Other',
  ]

  return (
    <>
      {/* Main Contact Section */}
      <NPISection className="bg-white pt-0">
        <div className="max-w-6xl mx-auto">
          <NPISectionHeader className="text-center mb-8">
            <NPISectionTitle className="text-[#8A3E25] text-2xl">{title}</NPISectionTitle>
            <NPISectionDescription className="text-[#725242] text-base">
              {description}
            </NPISectionDescription>
          </NPISectionHeader>

          <div className="grid lg:grid-cols-2 gap-6 max-w-5xl mx-auto">
            {/* Contact Form - Left Side */}
            <div>
              <NPICard className="bg-white border-2 border-[#25718A]/30 shadow-lg hover:shadow-xl transition-all duration-300">
                <NPICardHeader className="bg-[#25718A] p-4">
                  <NPICardTitle className="text-lg flex items-center gap-2 text-white">
                    <MessageSquare className="w-5 h-5" />
                    Send us a Message
                  </NPICardTitle>
                </NPICardHeader>
                <NPICardContent className="p-4">
                  <form onSubmit={handleSubmit} className="space-y-3">
                    <div className="grid md:grid-cols-2 gap-3">
                      <div>
                        <label
                          htmlFor="name"
                          className="block text-sm font-bold mb-1 font-npi text-[#8A3E25]"
                        >
                          Full Name *
                        </label>
                        <div className="relative">
                          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#25718A] w-4 h-4" />
                          <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                            className="w-full pl-9 pr-3 py-2 border-2 border-[#725242]/30 focus:border-[#25718A] bg-[#FFFFFF] focus:outline-none focus:ring-2 focus:ring-[#25718A]/30 font-npi transition-all duration-300 text-sm hover:border-[#25718A]/50 text-[#725242]"
                            placeholder="Enter your full name"
                          />
                        </div>
                      </div>

                      <div>
                        <label
                          htmlFor="email"
                          className="block text-sm font-bold mb-1 font-npi text-[#8A3E25]"
                        >
                          Email Address *
                        </label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#25718A] w-4 h-4" />
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                            className="w-full pl-9 pr-3 py-2 border-2 border-[#725242]/30 focus:border-[#25718A] bg-[#FFFFFF] focus:outline-none focus:ring-2 focus:ring-[#25718A]/30 font-npi transition-all duration-300 text-sm hover:border-[#25718A]/50 text-[#725242]"
                            placeholder="Enter your email address"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-3">
                      <div>
                        <label
                          htmlFor="organization"
                          className="block text-sm font-bold mb-1 font-npi text-[#8A3E25]"
                        >
                          Organization
                        </label>
                        <input
                          type="text"
                          id="organization"
                          name="organization"
                          value={formData.organization}
                          onChange={handleInputChange}
                          className="w-full px-3 py-2 border-2 border-[#725242]/30 focus:border-[#25718A] bg-[#FFFFFF] focus:outline-none focus:ring-2 focus:ring-[#25718A]/30 font-npi transition-all duration-300 text-sm hover:border-[#25718A]/50 text-[#725242]"
                          placeholder="Your organization (optional)"
                        />
                      </div>

                      <div>
                        <label
                          htmlFor="category"
                          className="block text-sm font-bold mb-1 font-npi text-[#8A3E25]"
                        >
                          Category *
                        </label>
                        <select
                          id="category"
                          name="category"
                          value={formData.category}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border-2 border-[#725242]/30 focus:border-[#25718A] bg-[#FFFFFF] focus:outline-none focus:ring-2 focus:ring-[#25718A]/30 font-npi transition-all duration-300 text-sm hover:border-[#25718A]/50 text-[#725242]"
                        >
                          <option value="">Select a category</option>
                          {categories.map((category) => (
                            <option key={category} value={category}>
                              {category}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="subject"
                        className="block text-sm font-bold mb-1 font-npi text-[#8A3E25]"
                      >
                        Subject *
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border-2 border-[#725242]/30 focus:border-[#25718A] bg-[#FFFFFF] focus:outline-none focus:ring-2 focus:ring-[#25718A]/30 font-npi transition-all duration-300 text-sm hover:border-[#25718A]/50 text-[#725242]"
                        placeholder="Brief subject of your message"
                      />
                    </div>

                    <div>
                      <label
                        htmlFor="message"
                        className="block text-sm font-bold mb-1 font-npi text-[#8A3E25]"
                      >
                        Message *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={4}
                        className="w-full px-3 py-2 border-2 border-[#725242]/30 focus:border-[#25718A] bg-[#FFFFFF] focus:outline-none focus:ring-2 focus:ring-[#25718A]/30 font-npi transition-all duration-300 resize-vertical text-sm hover:border-[#25718A]/50 text-[#725242]"
                        placeholder="Please provide details about your inquiry..."
                      />
                    </div>

                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                      <NPIButton
                        type="submit"
                        variant="primary"
                        size="md"
                        disabled={isSubmitting}
                        className="bg-[#25718A] hover:bg-[#1e5a6b] border-2 border-[#25718A] hover:border-[#1e5a6b] shadow-md hover:shadow-xl transition-all duration-300 text-sm transform hover:scale-105 hover:-translate-y-1 text-white"
                      >
                        {isSubmitting ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-2" />
                            Send Message
                          </>
                        )}
                      </NPIButton>

                      <p className="text-sm text-[#725242] font-npi">
                        We typically respond within 24-48 hours
                      </p>
                    </div>
                  </form>
                </NPICardContent>
              </NPICard>
            </div>

            {/* Map - Right Side */}
            <div>
              <NPIGoogleMap
                title="Find Us"
                description="Visit our office at the National Museums of Kenya, Museum Hill Road, Nairobi."
                height="h-[400px]"
              />
            </div>
          </div>
        </div>
      </NPISection>

      {/* FAQ Section */}
      <NPISection className="bg-[#EFE3BA] py-8">
        <div className="max-w-4xl mx-auto">
          <NPIFAQ />
        </div>
      </NPISection>

      {/* Contact Information Section */}
      <NPISection className="bg-white py-8 pb-0 mb-0">
        <div className="max-w-5xl mx-auto">
          <NPIContactInfo />
        </div>
      </NPISection>
    </>
  )
}
